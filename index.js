/**
 * Cloudflare Worker to detect and flag content scrapers on Gun Violence Archive
 * Protects search results pages and individual incidents
 * 
 * Features:
 * - Detects suspicious user agents and request patterns
 * - Identifies sequential incident ID access
 * - Monitors rate of requests and 404 errors
 * - Detects query-to-incident scraping pattern (accessing query results then crawling incidents)
 * - Identifies pagination usage in query results for comprehensive scraping
 * - Presents custom captcha challenge for suspicious users
 */

// Configuration
const getConfig = (env) => ({
  // Pages to protect (regex patterns)
  protectedPages: [
    /\/query\/\S+/, // Search results pages
    /\/incident\/\d+/   // Individual incident pages
  ],
  // Threshold for suspicious score to flag as scraper (from environment variables)
  suspiciousThreshold: parseInt(env.SUSPICIOUS_THRESHOLD || '80', 10), // Increased from 60 to 80 to reduce false positives
  // Time window for tracking request patterns (in milliseconds)
  patternTrackingWindow: parseInt(env.PATTERN_TRACKING_WINDOW || '3600000', 10), // Default: 1 hour
  // Maximum number of 404s before flagging as suspicious
  max404Threshold: parseInt(env.MAX_404_THRESHOLD || '10', 10), // Increased from 5 to 10 to be more tolerant of broken links
  // Maximum number of sequential incident IDs to access before flagging as suspicious
  maxSequentialIncidents: parseInt(env.MAX_SEQUENTIAL_INCIDENTS || '5', 10), // Increased from 3 to 5 to allow more natural browsing
  // Rate limit: maximum requests per minute
  maxRequestsPerMinute: parseInt(env.MAX_REQUESTS_PER_MINUTE || '60', 10), // Increased from 30 to 60 for more active users
  // Captcha configuration
  captcha: {
    // Time in milliseconds that a captcha challenge is valid
    validityPeriod: 1800000, // 30 minutes
    // Minimum time in milliseconds that a user should take to solve the captcha
    minSolveTime: 2000, // 2 seconds
    // Number of items to match in the captcha
    itemCount: 4
  }
});

// Known scraper user agents (partial matches)
const SUSPICIOUS_USER_AGENTS = [
  'headless',
  'phantomjs',
  'puppeteer',
  'selenium',
  'webdriver',
  'chrome-lighthouse',
  'googlebot',
  'bingbot',
  'yandexbot',
  'duckduckbot',
  'baiduspider',
  'python-requests',
  'python-urllib',
  'scrapy',
  'axios',
  'node-fetch',
  'wget',
  'curl',
  'go-http-client',
  'java/',
  'http.rb',
  'httpx',
  'aiohttp',
  'jsdom'
];

// Headers that should be present in legitimate browsers
const EXPECTED_HEADERS = [
  'user-agent',
  'accept',
  'accept-language',
  'accept-encoding',
  'referer'
];

// Client tracker with KV storage for persistence
// Falls back to in-memory cache if KV is not available
const createClientTracker = (env, config) => ({
  // Store client data with expiration
  cache: new Map(),

  // Get client identifier (IP + browser name)
  getClientId(request) {
    const ip = request.headers.get('cf-connecting-ip') || 'unknown';
    const ua = request.headers.get('user-agent') || 'unknown';

    // Extract browser name from user agent string
    let browserName = 'unknown';

    // Common browser detection patterns
    if (ua.includes('Chrome') && !ua.includes('Chromium') && !ua.includes('Edg') && !ua.includes('OPR') && !ua.includes('Safari/')) {
      browserName = 'Chrome';
    } else if (ua.includes('Firefox') && !ua.includes('Seamonkey')) {
      browserName = 'Firefox';
    } else if (ua.includes('Safari') && !ua.includes('Chrome') && !ua.includes('Chromium')) {
      browserName = 'Safari';
    } else if (ua.includes('Edg')) {
      browserName = 'Edge';
    } else if (ua.includes('OPR') || ua.includes('Opera')) {
      browserName = 'Opera';
    } else if (ua.includes('MSIE') || ua.includes('Trident/')) {
      browserName = 'IE';
    } else if (ua.includes('Seamonkey')) {
      browserName = 'Seamonkey';
    } else if (ua.includes('Chromium')) {
      browserName = 'Chromium';
    } else {
      // For bots and other browsers, use a simplified version of the UA
      // Extract the first word/token which often identifies the client
      const firstToken = ua.split(/[\s\/\(\)]/)[0];
      browserName = firstToken || 'unknown';
    }

    return `${ip}:${browserName}`;
  },

  // Initialize or get client data
  async getClientData(clientId) {
    // Try to get data from KV if available
    let data = null;
    if (env && env["gva-scrapers"]) {
      try {
        const kvData = await env["gva-scrapers"].get(`client:${clientId}`, { type: 'json' });
        if (kvData) {
          data = kvData;
          // Update cache with KV data
          this.cache.set(clientId, data);
        }
      } catch (error) {
        console.error('Error reading from KV:', error);
      }
    }

    // Fall back to in-memory cache if KV data not available
    if (!data) {
      if (!this.cache.has(clientId)) {
        this.cache.set(clientId, {
          firstSeen: Date.now(),
          lastSeen: Date.now(),
          requestCount: 0,
          requestsLastMinute: 0,
          lastMinuteTimestamp: Date.now(),
          notFoundCount: 0,
          incidentIds: [],
          sequentialIncidentCount: 0,
          paths: [],
          // New fields for query page and incident crawling detection
          lastQueryTimestamp: 0,
          queryPageVisits: [],
          incidentsAfterQuery: 0,
          paginationUses: 0,
          // Store suspicious activity logs directly in client data
          suspiciousLogs: []
        });
      }
      data = this.cache.get(clientId);
    }

    // Reset per-minute counters if needed
    if (Date.now() - data.lastMinuteTimestamp > 60000) {
      data.requestsLastMinute = 0;
      data.lastMinuteTimestamp = Date.now();
    }

    // Update last seen timestamp
    data.lastSeen = Date.now();

    return data;
  },

  // Save client data to KV
  async saveClientData(clientId, data) {
    // Update in-memory cache
    this.cache.set(clientId, data);

    // Save to KV if available
    if (env && env["gva-scrapers"]) {
      try {
        // Set expiration based on patternTrackingWindow (convert ms to seconds)
        const expirationTtl = Math.floor(config.patternTrackingWindow / 1000);
        await env["gva-scrapers"].put(`client:${clientId}`, JSON.stringify(data), { expirationTtl });
      } catch (error) {
        console.error('Error writing to KV:', error);
      }
    }
  },

  // Clean up expired entries (only needed for in-memory cache)
  cleanup() {
    const now = Date.now();
    for (const [clientId, data] of this.cache.entries()) {
      if (now - data.lastSeen > config.patternTrackingWindow) {
        this.cache.delete(clientId);
      }
    }
  },

  // Track a request
  async trackRequest(request, status) {
    const clientId = this.getClientId(request);
    const data = await this.getClientData(clientId);
    const url = new URL(request.url);
    const currentTime = Date.now();

    // Increment request counters
    data.requestCount++;
    data.requestsLastMinute++;

    // Track path
    data.paths.push({
      path: url.pathname,
      timestamp: currentTime,
      status: status
    });

    // Keep only the last 50 paths to avoid memory issues
    if (data.paths.length > 50) {
      data.paths.shift();
    }

    // Track 404s
    if (status === 404) {
      data.notFoundCount++;
    }

    // Check if this is a query page
    const isQueryPage = url.pathname.includes('/query');

    // Check if this is a pagination request on a query page
    const isPagination = isQueryPage && url.searchParams.has('page');

    if (isQueryPage) {
      // Record query page visit
      data.lastQueryTimestamp = currentTime;

      // Store query parameters
      const queryParams = {};
      for (const [key, value] of url.searchParams.entries()) {
        queryParams[key] = value;
      }

      data.queryPageVisits.push({
        timestamp: currentTime,
        path: url.pathname,
        params: queryParams
      });

      // Keep only the last 10 query visits
      if (data.queryPageVisits.length > 10) {
        data.queryPageVisits.shift();
      }

      // Track pagination usage
      if (isPagination) {
        data.paginationUses++;
      }
    }

    // Track incident IDs for sequential access detection
    const incidentMatch = url.pathname.match(/\/incident\/(\d+)/);
    if (incidentMatch) {
      const incidentId = parseInt(incidentMatch[1], 10);

      // Check for sequential access
      if (data.incidentIds.length > 0) {
        const lastIncidentId = data.incidentIds[data.incidentIds.length - 1];
        if (incidentId === lastIncidentId + 1 || incidentId === lastIncidentId - 1) {
          data.sequentialIncidentCount++;
        } else {
          data.sequentialIncidentCount = 0;
        }
      }

      data.incidentIds.push(incidentId);

      // Keep only the last 20 incident IDs
      if (data.incidentIds.length > 20) {
        data.incidentIds.shift();
      }

      // Check if this incident page visit follows a query page visit
      if (data.lastQueryTimestamp > 0 && 
          (currentTime - data.lastQueryTimestamp) < config.patternTrackingWindow) {
        data.incidentsAfterQuery++;
      }
    }

    // Save updated data to KV
    await this.saveClientData(clientId, data);

    // Run cleanup occasionally (1% chance per request) for in-memory cache
    if (Math.random() < 0.01) {
      this.cleanup();
    }

    return data;
  }
});

/**
 * Custom Captcha System
 * Implements a unique dragging matching game captcha for suspicious users
 */
const createCaptchaSystem = (env, config) => ({
  // Store captcha challenges with expiration
  cache: new Map(),

  // Update stats for challenges, failures, and completions
  async updateStats(isChallenge, isFailure, isCompletion) {
    try {
      if (env && env["gva-scrapers"]) {
        // Get current stats
        let stats = await env["gva-scrapers"].get('stats', { type: 'json' }) || {
          challenges: 0,
          failures: 0,
          completions: 0
        };

        // Update stats
        if (isChallenge) {
          stats.challenges = (stats.challenges || 0) + 1;
        }
        if (isFailure) {
          stats.failures = (stats.failures || 0) + 1;
        }
        if (isCompletion) {
          stats.completions = (stats.completions || 0) + 1;
        }

        // Save updated stats
        await env["gva-scrapers"].put('stats', JSON.stringify(stats));
      }
    } catch (error) {
      console.error('Error updating stats in KV:', error);
    }
  },

  // Clean up expired captchas for a specific client ID
  async cleanupOldCaptchas(clientId, currentToken) {
    // Find all captchas in the cache that belong to this client ID or are expired
    const captchasToRemove = [];
    const now = Date.now();
    for (const [token, challenge] of this.cache.entries()) {
      // Remove if it's an expired captcha or if it belongs to this client (except current token)
      if ((challenge.createdAt && now - challenge.createdAt > config.captcha.validityPeriod) || 
          (challenge.clientId === clientId && token !== currentToken)) {
        captchasToRemove.push(token);
      }
    }

    // Remove the old captchas from the cache
    for (const token of captchasToRemove) {
      this.cache.delete(token);
    }

    // Remove from KV if available
    if (env && env["gva-scrapers"]) {
      try {
        for (const token of captchasToRemove) {
          await env["gva-scrapers"].delete(`captcha:${token}`);
        }
      } catch (error) {
        console.error('Error deleting old captchas from KV:', error);
      }
    }

    return captchasToRemove.length;
  },

  // Generate a unique captcha challenge
  async generateChallenge(clientId) {
    // Track that a challenge was generated
    this.updateStats(true, false, false);

    // Create items for the matching game
    const items = [];
    // Define shapes with their CSS properties instead of class names
    const shapes = [
      { name: 'circle', styles: 'border-radius: 50%;' },
      { name: 'square', styles: 'border-radius: 0;' },
      { name: 'triangle', styles: 'width: 0; height: 0; border-left: 20px solid transparent; border-right: 20px solid transparent; border-bottom: 40px solid currentColor; background-color: transparent !important;' },
      { name: 'star', styles: 'clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);' },
      { name: 'diamond', styles: 'transform: rotate(45deg);' },
      { name: 'hexagon', styles: 'clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);' },
      { name: 'heart', styles: 'clip-path: path(\'M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z\');' },
      { name: 'moon', styles: 'border-radius: 50%; box-shadow: 5px 0 0 0 currentColor; background-color: transparent !important;' },
      { name: 'cross', styles: 'background: linear-gradient(to bottom, transparent 35%, currentColor 35%, currentColor 65%, transparent 65%), linear-gradient(to right, transparent 35%, currentColor 35%, currentColor 65%, transparent 65%);' },
      { name: 'pentagon', styles: 'clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);' },
      { name: 'trapezoid', styles: 'clip-path: polygon(20% 0%, 80% 0%, 100% 100%, 0% 100%);' },
      { name: 'oval', styles: 'border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;' },
      { name: 'parallelogram', styles: 'transform: skew(-20deg);' },
      { name: 'arrow', styles: 'clip-path: polygon(0% 20%, 60% 20%, 60% 0%, 100% 50%, 60% 100%, 60% 80%, 0% 80%);' },
      { name: 'crescent', styles: 'border-radius: 50%; box-shadow: -8px 0 0 0 currentColor; background-color: transparent !important;' },
      { name: 'octagon', styles: 'clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);' },
      { name: 'rhombus', styles: 'transform: skew(30deg, 0deg);' },
      { name: 'semicircle', styles: 'border-radius: 100px 100px 0 0;' },
      { name: 'plus', styles: 'clip-path: polygon(35% 0%, 65% 0%, 65% 35%, 100% 35%, 100% 65%, 65% 65%, 65% 100%, 35% 100%, 35% 65%, 0% 65%, 0% 35%, 35% 35%);' },
      { name: 'chevron', styles: 'clip-path: polygon(75% 0%, 100% 50%, 75% 100%, 50% 75%, 75% 50%, 50% 25%);' },
      { name: 'ring', styles: 'border: 8px solid currentColor; border-radius: 50%; background-color: transparent !important;' },
      { name: 'lightning', styles: 'clip-path: polygon(40% 0%, 80% 0%, 60% 50%, 100% 50%, 20% 100%, 40% 50%, 0% 50%);' },
      { name: 'teardrop', styles: 'border-radius: 50% 0 50% 50%;' },
      { name: 'hourglass', styles: 'clip-path: polygon(0 0, 100% 0, 100% 20%, 50% 50%, 100% 80%, 100% 100%, 0 100%, 0 80%, 50% 50%, 0 20%);' },
      { name: 'cloud', styles: 'border-radius: 50%; box-shadow: 10px -5px 0 -2px currentColor, 20px 0px 0 -3px currentColor; background-color: currentColor;' },
      { name: 'puzzle', styles: 'clip-path: polygon(0 0, 33% 0, 33% 33%, 66% 33%, 66% 0, 100% 0, 100% 66%, 66% 66%, 66% 100%, 33% 100%, 33% 66%, 0 66%);' },
      { name: 'wave', styles: 'clip-path: polygon(0 40%, 20% 20%, 40% 40%, 60% 20%, 80% 40%, 100% 20%, 100% 100%, 0 100%);' },
      { name: 'shield', styles: 'clip-path: polygon(0 0, 100% 0, 100% 75%, 50% 100%, 0 75%);' },
      { name: 'sun', styles: 'background-color: currentColor; border-radius: 50%; box-shadow: 0 0 0 5px transparent, 0 0 0 8px currentColor, 0 0 0 12px transparent, 0 0 0 15px currentColor, 0 0 0 20px transparent;' },
      { name: 'gear', styles: 'clip-path: polygon(50% 0%, 38% 12%, 50% 15%, 38% 20%, 50% 25%, 38% 30%, 50% 38%, 38% 50%, 50% 62%, 38% 70%, 50% 75%, 38% 80%, 50% 85%, 38% 88%, 50% 100%, 62% 88%, 50% 85%, 62% 80%, 50% 75%, 62% 70%, 50% 62%, 62% 50%, 50% 38%, 62% 30%, 50% 25%, 62% 20%, 50% 15%, 62% 12%);' },
      { name: 'spiral', styles: 'background: conic-gradient(currentColor 0%, transparent 40%, currentColor 80%); border-radius: 50%;' },
      { name: 'clover', styles: 'background: radial-gradient(circle at 0% 50%, currentColor 25%, transparent 25%), radial-gradient(circle at 100% 50%, currentColor 25%, transparent 25%), radial-gradient(circle at 50% 0%, currentColor 25%, transparent 25%), radial-gradient(circle at 50% 100%, currentColor 25%, transparent 25%);' },
      { name: 'cube', styles: 'clip-path: polygon(25% 25%, 75% 25%, 75% 75%, 25% 75%); transform: perspective(100px) rotateX(20deg) rotateY(20deg);' },
      { name: 'crown', styles: 'clip-path: polygon(0% 65%, 10% 40%, 30% 65%, 50% 20%, 70% 65%, 90% 40%, 100% 65%, 100% 100%, 0% 100%);' },
      { name: 'fish', styles: 'clip-path: polygon(0 50%, 25% 20%, 75% 20%, 100% 50%, 75% 80%, 25% 80%); border-radius: 50%;' },
      { name: 'leaf', styles: 'border-radius: 0 50% 0 50%; transform: rotate(45deg);' },
      { name: 'bowtie', styles: 'clip-path: polygon(0 0, 50% 50%, 0 100%, 100% 100%, 50% 50%, 100% 0);' },
      { name: 'egg', styles: 'border-radius: 50% 50% 50% 50% / 60% 60% 70% 70%;' },
      { name: 'lock', styles: 'clip-path: polygon(25% 0, 75% 0, 75% 40%, 100% 40%, 100% 100%, 0 100%, 0 40%, 25% 40%);' },
      { name: 'key', styles: 'clip-path: polygon(0 15%, 15% 15%, 15% 0, 30% 0, 30% 15%, 85% 15%, 85% 30%, 70% 30%, 70% 45%, 85% 45%, 85% 60%, 70% 60%, 70% 75%, 85% 75%, 85% 90%, 0 90%);' },
      { name: 'bell', styles: 'clip-path: polygon(35% 0%, 65% 0%, 85% 15%, 85% 55%, 100% 70%, 100% 85%, 0% 85%, 0% 70%, 15% 55%, 15% 15%);' },
      { name: 'drop', styles: 'border-radius: 0 50% 50% 50%; transform: rotate(45deg);' },
      { name: 'flower', styles: 'background: radial-gradient(circle at 30% 30%, currentColor 10%, transparent 11%), radial-gradient(circle at 70% 30%, currentColor 10%, transparent 11%), radial-gradient(circle at 30% 70%, currentColor 10%, transparent 11%), radial-gradient(circle at 70% 70%, currentColor 10%, transparent 11%), radial-gradient(circle at 50% 50%, currentColor 35%, transparent 36%);' },
      { name: 'pyramid', styles: 'clip-path: polygon(50% 0%, 100% 100%, 0% 100%);' },
      { name: 'ribbon', styles: 'clip-path: polygon(50% 0%, 90% 20%, 100% 60%, 75% 100%, 50% 85%, 25% 100%, 0% 60%, 10% 20%);' },
      { name: 'flag', styles: 'clip-path: polygon(0 0, 70% 0, 60% 25%, 70% 50%, 60% 75%, 70% 100%, 0 100%);' },
      { name: 'guitar', styles: 'clip-path: ellipse(30% 40% at 50% 50%); transform: rotate(45deg);' },
      { name: 'anchor', styles: 'clip-path: polygon(40% 0, 60% 0, 60% 20%, 100% 20%, 100% 40%, 80% 40%, 80% 60%, 100% 80%, 80% 100%, 60% 80%, 50% 100%, 40% 80%, 20% 100%, 0 80%, 20% 60%, 20% 40%, 0 40%, 0 20%, 40% 20%);' }
    ];
    // Define colors with their CSS values instead of class names
    const colors = [
      { name: 'red', value: '#f44336' },
      { name: 'blue', value: '#2196f3' },
      { name: 'green', value: '#4caf50' },
      { name: 'yellow', value: '#ffeb3b' },
      { name: 'purple', value: '#9c27b0' },
      { name: 'orange', value: '#ff9800' },
      { name: 'teal', value: '#009688' },
      { name: 'pink', value: '#e91e63' },
      { name: 'brown', value: '#795548' },
      { name: 'cyan', value: '#00bcd4' },
      { name: 'lime', value: '#cddc39' },
      { name: 'indigo', value: '#3f51b5' },
      { name: 'amber', value: '#ffc107' },
      { name: 'deep-purple', value: '#673ab7' },
      { name: 'light-blue', value: '#03a9f4' },
      { name: 'deep-orange', value: '#ff5722' }
    ];

    // Randomly select a subset of shapes to use for this challenge
    // We need at least config.captcha.itemCount unique shapes
    if (shapes.length < config.captcha.itemCount) {
      console.error(`Not enough unique shapes (${shapes.length}) for captcha challenge (${config.captcha.itemCount} required)`);
      // Fallback: use all available shapes
      return this.generateChallenge(clientId);
    }

    // Create a copy of the shapes array to avoid modifying the original
    const availableShapes = [...shapes];
    const selectedShapes = [];

    // Select exactly config.captcha.itemCount unique shapes
    for (let i = 0; i < config.captcha.itemCount; i++) {
      const randomIndex = Math.floor(Math.random() * availableShapes.length);
      selectedShapes.push(availableShapes[randomIndex]);
      // Remove the selected shape from availableShapes to ensure it's not selected again
      availableShapes.splice(randomIndex, 1);
    }

    // Generate random pairs of items to match
    const leftItems = [];
    const rightItems = [];

    // Create pairs of items for each selected shape
    for (let i = 0; i < config.captcha.itemCount; i++) {
      // Use each shape exactly once
      const shape = selectedShapes[i];

      // Generate two different random colors for the pair
      let colorIndex1, colorIndex2;
      do {
        colorIndex1 = Math.floor(Math.random() * colors.length);
        colorIndex2 = Math.floor(Math.random() * colors.length);
      } while (colorIndex1 === colorIndex2);

      const color1 = colors[colorIndex1];
      const color2 = colors[colorIndex2];

      // Generate random rotation for each item
      const rotation1 = Math.floor(Math.random() * 360);
      const rotation2 = Math.floor(Math.random() * 360);

      // Randomly decide which item goes in which column
      const randomPosition = Math.random() < 0.5;

      const item1 = {
        id: `item-${i}-1`,
        shapeName: shape.name,
        shapeStyles: shape.styles,
        colorName: color1.name,
        colorValue: color1.value,
        position: randomPosition ? 'left' : 'right',
        matchId: `item-${i}-2`,
        rotation: rotation1
      };

      const item2 = {
        id: `item-${i}-2`,
        shapeName: shape.name,
        shapeStyles: shape.styles,
        colorName: color2.name,
        colorValue: color2.value,
        position: randomPosition ? 'right' : 'left',
        matchId: `item-${i}-1`,
        rotation: rotation2
      };

      // Add items to their respective columns
      if (randomPosition) {
        leftItems.push(item1);
        rightItems.push(item2);
      } else {
        leftItems.push(item2);
        rightItems.push(item1);
      }
    }

    // Shuffle each column separately to ensure different ordering on both sides
    for (let i = leftItems.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [leftItems[i], leftItems[j]] = [leftItems[j], leftItems[i]];
    }

    for (let i = rightItems.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [rightItems[i], rightItems[j]] = [rightItems[j], rightItems[i]];
    }

    // Combine all items for the challenge
    items.push(...leftItems, ...rightItems);

    // Create a unique token and challenge ID for this challenge
    const token = crypto.randomUUID();
    const challengeId = crypto.randomUUID();

    // Store the challenge
    const challenge = {
      token,
      challengeId,
      items,
      createdAt: Date.now(),
      solved: false,
      clientId
    };

    this.cache.set(token, challenge);

    // Store in KV if available
    if (env && env["gva-scrapers"]) {
      try {
        const expirationTtl = Math.floor(config.captcha.validityPeriod / 1000);
        await env["gva-scrapers"].put(`captcha:${token}`, JSON.stringify(challenge), { expirationTtl });
      } catch (error) {
        console.error('Error storing captcha in KV:', error);
      }
    }

    return challenge;
  },

  // Get a challenge by token
  async getChallenge(token) {
    // Try to get from cache first
    if (this.cache.has(token)) {
      return this.cache.get(token);
    }

    // Try to get from KV if available
    if (env && env["gva-scrapers"]) {
      try {
        const challenge = await env["gva-scrapers"].get(`captcha:${token}`, { type: 'json' });
        if (challenge) {
          this.cache.set(token, challenge);
          return challenge;
        }
      } catch (error) {
        console.error('Error reading captcha from KV:', error);
      }
    }

    return null;
  },

  // Validate a captcha solution
  async validateSolution(token, solution) {
    const challenge = await this.getChallenge(token);

    // Check if challenge exists and is not expired
    if (!challenge) {
      // Track that a solution failed
      this.updateStats(false, true, false);
      return { valid: false, reason: 'Challenge not found or expired' };
    }

    // Check if challenge is already solved
    if (challenge.solved) {
      // Track that a solution failed
      this.updateStats(false, true, false);
      return { valid: false, reason: 'Challenge already solved' };
    }

    // Check if challenge is expired
    if (Date.now() - challenge.createdAt > config.captcha.validityPeriod) {
      // Track that a solution failed
      this.updateStats(false, true, false);
      return { valid: false, reason: 'Challenge expired' };
    }

    // Check if solution was submitted too quickly (bot detection)
    if (Date.now() - challenge.createdAt < config.captcha.minSolveTime) {
      // Track that a solution failed
      this.updateStats(false, true, false);
      return { valid: false, reason: 'Solution submitted too quickly' };
    }

    // Validate the solution
    try {
      const matches = JSON.parse(solution);

      // Check if all items are matched correctly
      const correctMatches = challenge.items.every(item => {
        const match = matches.find(m => m.item === item.id);
        return match && match.matched === item.matchId;
      });

      if (correctMatches) {
        // Mark challenge as solved
        challenge.solved = true;

        // Update in cache
        this.cache.set(token, challenge);

        // Update in KV if available
        if (env && env["gva-scrapers"]) {
          try {
            const expirationTtl = Math.floor(config.captcha.validityPeriod / 1000);
            await env["gva-scrapers"].put(`captcha:${token}`, JSON.stringify(challenge), { expirationTtl });
          } catch (error) {
            console.error('Error updating captcha in KV:', error);
          }
        }

        // Track that a solution succeeded
        this.updateStats(false, false, true);

        // Clean up other captcha entries for this client
        if (challenge.clientId) {
          await this.cleanupOldCaptchas(challenge.clientId, token);
        }

        return { valid: true };
      } else {
        // Track that a solution failed
        this.updateStats(false, true, false);

        return { valid: false, reason: 'Incorrect matches' };
      }
    } catch (error) {
      // Track that a solution failed
      this.updateStats(false, true, false);
      return { valid: false, reason: 'Invalid solution format' };
    }
  },

  // Generate HTML for the captcha page
  generateCaptchaHtml(challenge, originalUrl) {
    // Generate random class and ID names to make it harder for bots to target elements
    const randomClassNames = {
      instructions: `instructions-${Math.random().toString(36).substring(2, 8)}`,
      captchaContainer: `captcha-container-${Math.random().toString(36).substring(2, 8)}`,
      gameArea: `game-area-${Math.random().toString(36).substring(2, 8)}`,
      column: `column-${Math.random().toString(36).substring(2, 8)}`,
      leftColumn: `left-column-${Math.random().toString(36).substring(2, 8)}`,
      rightColumn: `right-column-${Math.random().toString(36).substring(2, 8)}`,
      item: `item-${Math.random().toString(36).substring(2, 8)}`,
      dragging: `dragging-${Math.random().toString(36).substring(2, 8)}`,
      matched: `matched-${Math.random().toString(36).substring(2, 8)}`,
      selected: `selected-${Math.random().toString(36).substring(2, 8)}`,
      submitBtn: `submit-btn-${Math.random().toString(36).substring(2, 8)}`,
      errorMessage: `error-message-${Math.random().toString(36).substring(2, 8)}`
    };

    const randomIds = {
      leftColumn: `left-column-${Math.random().toString(36).substring(2, 8)}`,
      rightColumn: `right-column-${Math.random().toString(36).substring(2, 8)}`,
      submitBtn: `submit-btn-${Math.random().toString(36).substring(2, 8)}`,
      errorMessage: `error-message-${Math.random().toString(36).substring(2, 8)}`
    };

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Security Check</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #2c3e50;
      margin-bottom: 20px;
    }
    .${randomClassNames.instructions} {
      background-color: #f8f9fa;
      border-left: 4px solid #4285f4;
      padding: 15px;
      margin-bottom: 20px;
    }
    .${randomClassNames.captchaContainer} {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-top: 30px;
    }
    .${randomClassNames.gameArea} {
      display: flex;
      justify-content: space-between;
      gap: 20px;
    }
    .${randomClassNames.column} {
      flex: 1;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      min-height: 300px;
    }
    .${randomClassNames.item} {
      width: 60px;
      height: 60px;
      margin: 10px;
      cursor: grab;
      position: relative;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      user-select: none;
    }
    .${randomClassNames.item}.${randomClassNames.dragging} {
      opacity: 0.5;
    }
    .${randomClassNames.item}.${randomClassNames.matched} {
      border: 2px solid #4caf50;
    }
    .${randomClassNames.item}.${randomClassNames.selected} {
      outline: 2px dashed #4285f4;
    }
    /* Shape and color classes removed and replaced with inline styles */
    .${randomClassNames.submitBtn} {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 10px 20px;
      font-size: 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 20px;
    }
    .${randomClassNames.submitBtn}:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .refreshLink {
      color: #4285f4;
      text-decoration: underline;
      cursor: pointer;
      margin-left: 20px;
      font-size: 14px;
    }
    .${randomClassNames.errorMessage} {
      color: #f44336;
      margin-top: 10px;
      display: none;
    }
  </style>
</head>
<body>
  <h1>Security Check Required</h1>

  <div class="${randomClassNames.instructions}">
    <p>Our system has detected unusual activity from your connection. To continue to the site, please complete the security check below.</p>
    <p><strong>Instructions:</strong> Match each shape on the left with its identical shape on the right by dragging and dropping. Match by shape only - the colors and orientation may be different.</p>
  </div>

  <div class="${randomClassNames.captchaContainer}">
    <div style="display: flex; justify-content: flex-end; margin-bottom: 10px;">
      <span class="refreshLink" id="refreshCaptcha">Get new symbols</span>
    </div>

    <div class="${randomClassNames.gameArea}">
      <div class="${randomClassNames.column} ${randomClassNames.leftColumn}" id="${randomIds.leftColumn}">
        ${challenge.items.filter(item => item.position === 'left').map(item => `
          <div class="${randomClassNames.item}" id="${item.id}" draggable="true">
            <div style="width: 40px; height: 40px; ${item.shapeStyles} background-color: ${item.colorValue}; transform: rotate(${item.rotation}deg);"></div>
          </div>
        `).join('')}
      </div>
      <div class="${randomClassNames.column} ${randomClassNames.rightColumn}" id="${randomIds.rightColumn}">
        ${challenge.items.filter(item => item.position === 'right').map(item => `
          <div class="${randomClassNames.item}" id="${item.id}" draggable="true">
            <div style="width: 40px; height: 40px; ${item.shapeStyles} background-color: ${item.colorValue}; transform: rotate(${item.rotation}deg);"></div>
          </div>
        `).join('')}
      </div>
    </div>

    <div class="${randomClassNames.errorMessage}" id="${randomIds.errorMessage}">Incorrect matches. Please try again.</div>

    <div style="display: flex; align-items: center;">
      <button class="${randomClassNames.submitBtn}" id="${randomIds.submitBtn}" disabled>Verify</button>
    </div>
  </div>

  <script>
    // Store the matches
    const matches = [];
    let selectedItem = null;

    // Track when the captcha was loaded
    const startTime = Date.now();

    // Mouse movement tracking
    const mouseMovements = [];
    let lastMousePosition = null;
    let isTrackingMouse = false;

    // Get DOM elements
    const items = document.querySelectorAll('.${randomClassNames.item}');
    const submitBtn = document.getElementById('${randomIds.submitBtn}');
    const errorMessage = document.getElementById('${randomIds.errorMessage}');
    const captchaContainer = document.querySelector('.${randomClassNames.captchaContainer}');

    // Add event listeners for drag and drop
    items.forEach(item => {
      item.addEventListener('dragstart', handleDragStart);
      item.addEventListener('dragover', handleDragOver);
      item.addEventListener('drop', handleDrop);
      item.addEventListener('dragend', handleDragEnd);
      item.addEventListener('click', handleClick);
    });

    // Add mouse movement tracking
    captchaContainer.addEventListener('mouseenter', () => {
      isTrackingMouse = true;
    });

    captchaContainer.addEventListener('mouseleave', () => {
      isTrackingMouse = false;
    });

    captchaContainer.addEventListener('mousemove', trackMouseMovement);

    // Submit button event listener
    submitBtn.addEventListener('click', handleSubmit);

    // Refresh captcha link event listener
    document.getElementById('refreshCaptcha').addEventListener('click', function(e) {
      e.preventDefault();
      // Get the current URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const originalUrl = urlParams.get('url') || '/';

      // Show loading state
      this.textContent = 'Loading...';
      this.style.opacity = '0.7';

      // Make a request to the server to generate a new token
      fetch('/captcha-challenge', {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-Refresh-Captcha': 'true'
        }
      })
      .then(response => {
        // Redirect to a new captcha challenge page without the token
        // The server will generate a new token and redirect to the proper page
        const captchaUrl = new URL(window.location.origin + '/captcha-challenge');
        captchaUrl.searchParams.set('url', originalUrl);
        // Don't include the refresh parameter in the URL to avoid redirect loops
        // Instead, use the X-Refresh-Captcha header which is only sent in the fetch request
        window.location.href = captchaUrl.toString();
      })
      .catch(error => {
        console.error('Error refreshing captcha:', error);
        // Fallback to the old method if the request fails
        const captchaUrl = new URL(window.location.href);
        captchaUrl.searchParams.set('t', Date.now());
        window.location.href = captchaUrl.toString();
      });
    });

    function handleDragStart(e) {
      this.classList.add('${randomClassNames.dragging}');
      e.dataTransfer.setData('text/plain', this.id);
      selectedItem = this;
    }

    function handleDragOver(e) {
      e.preventDefault();
    }

    function handleDrop(e) {
      e.preventDefault();
      const draggedItemId = e.dataTransfer.getData('text/plain');
      const draggedItem = document.getElementById(draggedItemId);
      const targetItem = this;

      // Don't allow dropping on itself
      if (draggedItem === targetItem) return;

      // Check if items are in different columns
      const draggedColumn = draggedItem.parentElement;
      const targetColumn = targetItem.parentElement;

      if (draggedColumn !== targetColumn) {
        // Match the items
        matchItems(draggedItem, targetItem);
      }
    }

    function handleDragEnd() {
      this.classList.remove('${randomClassNames.dragging}');
    }

    function handleClick(e) {
      if (selectedItem) {
        // If an item is already selected and we click on another item in a different column
        if (selectedItem !== this && selectedItem.parentElement !== this.parentElement) {
          matchItems(selectedItem, this);
        }

        // Deselect the item
        selectedItem.classList.remove('${randomClassNames.selected}');
        selectedItem = null;
      } else {
        // Select this item
        this.classList.add('${randomClassNames.selected}');
        selectedItem = this;
      }
    }

    function trackMouseMovement(e) {
      if (!isTrackingMouse) return;

      const currentTime = Date.now();
      const currentPosition = { x: e.clientX, y: e.clientY, time: currentTime };

      // If we have a previous position, calculate movement metrics
      if (lastMousePosition) {
        const timeDiff = currentTime - lastMousePosition.time; // Time since last movement in ms

        // Only record if there's a meaningful time difference (avoid duplicate events)
        if (timeDiff > 5) {
          const xDiff = currentPosition.x - lastMousePosition.x;
          const yDiff = currentPosition.y - lastMousePosition.y;

          // Calculate distance using Pythagorean theorem
          const distance = Math.sqrt(xDiff * xDiff + yDiff * yDiff);

          // Calculate speed in pixels per second
          const speed = distance / (timeDiff / 1000);

          // Calculate angle of movement in radians, then convert to degrees
          const angle = Math.atan2(yDiff, xDiff) * (180 / Math.PI);

          // Store the movement data
          mouseMovements.push({
            x: currentPosition.x,
            y: currentPosition.y,
            time: currentTime,
            timeDiff,
            distance,
            speed,
            angle
          });

          // Limit the number of stored movements to prevent memory issues
          if (mouseMovements.length > 1000) {
            mouseMovements.shift();
          }
        }
      }

      // Update last position
      lastMousePosition = currentPosition;
    }

    function analyzeMouseMovements() {
      // If we don't have enough data, return empty metrics
      if (mouseMovements.length < 5) {
        return {
          sufficientData: false,
          message: "Not enough mouse movement data"
        };
      }

      // Calculate various metrics for bot detection
      const speeds = mouseMovements.map(m => m.speed);
      const distances = mouseMovements.map(m => m.distance);
      const angles = mouseMovements.map(m => m.angle);
      const timeDiffs = mouseMovements.map(m => m.timeDiff);

      // Calculate average, min, max, and standard deviation of speeds
      const avgSpeed = speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
      const minSpeed = Math.min(...speeds);
      const maxSpeed = Math.max(...speeds);
      const speedVariance = speeds.reduce((sum, speed) => sum + Math.pow(speed - avgSpeed, 2), 0) / speeds.length;
      const speedStdDev = Math.sqrt(speedVariance);

      // Calculate average, min, max, and standard deviation of time differences
      const avgTimeDiff = timeDiffs.reduce((sum, diff) => sum + diff, 0) / timeDiffs.length;
      const minTimeDiff = Math.min(...timeDiffs);
      const maxTimeDiff = Math.max(...timeDiffs);
      const timeDiffVariance = timeDiffs.reduce((sum, diff) => sum + Math.pow(diff - avgTimeDiff, 2), 0) / timeDiffs.length;
      const timeDiffStdDev = Math.sqrt(timeDiffVariance);

      // Detect straight lines (consistent angles)
      const angleChanges = [];
      for (let i = 1; i < angles.length; i++) {
        let change = Math.abs(angles[i] - angles[i-1]);
        // Handle angle wrapping (e.g., from 350° to 10° is a 20° change, not 340°)
        if (change > 180) change = 360 - change;
        angleChanges.push(change);
      }

      const avgAngleChange = angleChanges.reduce((sum, change) => sum + change, 0) / angleChanges.length;
      const straightLineRatio = angleChanges.filter(change => change < 5).length / angleChanges.length;

      // Detect teleportation (unusually large distances in short time)
      const teleportCount = mouseMovements.filter(m => m.distance > 100 && m.timeDiff < 50).length;

      // Detect unnatural speed consistency
      const speedConsistencyRatio = speedStdDev / avgSpeed;

      // Detect unnatural timing consistency
      const timingConsistencyRatio = timeDiffStdDev / avgTimeDiff;

      // Calculate path efficiency (straight line distance / total path distance)
      const startPoint = { x: mouseMovements[0].x, y: mouseMovements[0].y };
      const endPoint = { x: mouseMovements[mouseMovements.length - 1].x, y: mouseMovements[mouseMovements.length - 1].y };
      const straightLineDistance = Math.sqrt(
        Math.pow(endPoint.x - startPoint.x, 2) + 
        Math.pow(endPoint.y - startPoint.y, 2)
      );
      const totalPathDistance = distances.reduce((sum, distance) => sum + distance, 0);
      const pathEfficiency = straightLineDistance / totalPathDistance;

      // Return all metrics
      return {
        sufficientData: true,
        movementCount: mouseMovements.length,
        speed: {
          avg: avgSpeed,
          min: minSpeed,
          max: maxSpeed,
          stdDev: speedStdDev,
          consistency: speedConsistencyRatio
        },
        timing: {
          avg: avgTimeDiff,
          min: minTimeDiff,
          max: maxTimeDiff,
          stdDev: timeDiffStdDev,
          consistency: timingConsistencyRatio
        },
        direction: {
          avgAngleChange,
          straightLineRatio
        },
        path: {
          efficiency: pathEfficiency,
          teleportCount
        },
        // Add bot detection flags
        botIndicators: {
          unnaturalStraightLines: straightLineRatio > 0.7,
          teleportation: teleportCount > 0,
          tooConsistentSpeed: speedConsistencyRatio < 0.2,
          tooConsistentTiming: timingConsistencyRatio < 0.2,
          tooEfficientPath: pathEfficiency > 0.95
        }
      };
    }

    function matchItems(item1, item2) {
      // Add matched class to both items
      item1.classList.add('${randomClassNames.matched}');
      item2.classList.add('${randomClassNames.matched}');

      // Store the match
      matches.push({
        item: item1.id,
        matched: item2.id
      });

      matches.push({
        item: item2.id,
        matched: item1.id
      });

      // Enable submit button if all items are matched, but add a random delay
      if (matches.length === ${challenge.items.length}) {
        const elapsedTime = Date.now() - startTime;

        // If solved too quickly, add a random delay before enabling the button
        if (elapsedTime < 5000) {
          const randomDelay = 2000 + Math.random() * 3000; // 2-5 seconds
          setTimeout(() => {
            submitBtn.disabled = false;
          }, randomDelay);
        } else {
          submitBtn.disabled = false;
        }
      }
    }

    function handleSubmit() {
      // Disable the button to prevent multiple submissions
      submitBtn.disabled = true;

      // Calculate mouse movement metrics for bot detection
      const mouseMetrics = analyzeMouseMovements();

      // Send the solution to the server
      fetch('https://cap.gunviolencearchive.org/captcha-verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: '${challenge.token}',
          challengeId: '${challenge.challengeId}',
          solution: JSON.stringify(matches),
          originalUrl: '${originalUrl}',
          startTime: startTime,
          endTime: Date.now(),
          mouseData: {
            movements: mouseMovements.length > 100 ? mouseMovements.filter((_, i) => i % Math.ceil(mouseMovements.length / 100) === 0) : mouseMovements,
            metrics: mouseMetrics
          }
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Redirect to the original URL
          window.location.href = data.redirectUrl;
        } else {
          if (data.newCaptcha) {
            // Redirect to the new captcha challenge
            const captchaUrl = new URL('https://cap.gunviolencearchive.org');
            captchaUrl.pathname = '/captcha-challenge';
            captchaUrl.search = \`?token=\${data.newCaptcha.token}&url=\${encodeURIComponent(originalUrl)}\`;
            window.location.href = captchaUrl.toString();
          } else {
            // If no new captcha is provided, reload the page to get a new captcha
            window.location.reload();
          }
        }
      })
      .catch(error => {
        console.error('Error:', error);
        errorMessage.style.display = 'block';
        submitBtn.disabled = false;
      });
    }
  </script>
</body>
</html>`;
  }
});

/**
 * Export default function for Cloudflare Workers
 */
export default {
  async fetch(request, env, ctx) {
    return await handleRequest(request, env, ctx);
  }
};

/**
 * Handle incoming requests
 * @param {Request} request - The incoming request
 * @param {Object} env - Environment variables
 * @param {Object} ctx - Execution context
 * @returns {Promise<Response>} - The response
 */
async function handleRequest(request, env, ctx) {
  // Initialize configuration and services
  const config = getConfig(env);
  const clientTracker = createClientTracker(env, config);
  const captchaSystem = createCaptchaSystem(env, config);

  // Get the URL and check if it's a protected page
  const url = new URL(request.url);
  const isProtectedPage = config.protectedPages.some(pattern => pattern.test(url.pathname));
  const clientId = clientTracker.getClientId(request);

  // TEMPORARY RESTRICTION: Only process requests from specific IP, bypass worker for others
  // const clientIp = request.headers.get('cf-connecting-ip') || 'unknown';
  // if (clientIp !== '***************') {
  //   // Bypass the worker and pass the request directly to the origin
  //   return fetch(request);
  // }

  // Check if this is a captcha solution submission
  if (request.method === 'POST' && url.pathname.startsWith('/captcha-verify')) {
    try {
      // Parse the request body
      const body = await request.json();
      const { token, challengeId, solution, originalUrl, startTime, endTime, mouseData } = body;

      // Check if the solving time is reasonable
      const solvingTime = endTime - startTime;

      // If solving time is too short (less than 5 seconds), it's likely a bot
      if (solvingTime < 5000) {
        // Generate a new captcha challenge for the user
        const newChallenge = await captchaSystem.generateChallenge(clientId);

        return new Response(JSON.stringify({
          success: false,
          error: 'Solution submitted too quickly',
          newCaptcha: {
            token: newChallenge.token,
            challengeId: newChallenge.challengeId
          }
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Validate mouse movement data if present
      if (mouseData) {
        const mouseValidation = validateMouseMovements(mouseData);
        if (!mouseValidation.valid) {
          // Generate a new captcha challenge for the user
          const newChallenge = await captchaSystem.generateChallenge(clientId);

          return new Response(JSON.stringify({
            success: false,
            error: mouseValidation.reason,
            newCaptcha: {
              token: newChallenge.token,
              challengeId: newChallenge.challengeId
            }
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      // If solving time is suspiciously long (more than 30 minutes), it might be a replay attack
      if (solvingTime > 1800000) {
        // Generate a new captcha challenge for the user
        const newChallenge = await captchaSystem.generateChallenge(clientId);

        return new Response(JSON.stringify({
          success: false,
          error: 'Solution expired',
          newCaptcha: {
            token: newChallenge.token,
            challengeId: newChallenge.challengeId
          }
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Get the challenge
      const challenge = await captchaSystem.getChallenge(token);

      // Verify that the challenge exists and the challengeId matches
      if (!challenge || challenge.challengeId !== challengeId) {
        // Generate a new captcha challenge for the user
        const newChallenge = await captchaSystem.generateChallenge(clientId);

        return new Response(JSON.stringify({
          success: false,
          error: 'Invalid challenge',
          newCaptcha: {
            token: newChallenge.token,
            challengeId: newChallenge.challengeId
          }
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Validate the solution
      const validationResult = await captchaSystem.validateSolution(token, solution);

      if (validationResult.valid) {
        // Store that this client has passed the captcha
        if (env && env["gva-scrapers"]) {
          // Set a 24-hour expiration (86400 seconds)
          await env["gva-scrapers"].put(`captcha-passed:${clientId}`, 'true', { expirationTtl: 86400 });

          // Remove the suspicious flag
          await env["gva-scrapers"].delete(`suspicious-flag:${clientId}`);

          // Reset the client's stats by creating a new entry
          const clientTracker = createClientTracker(env, getConfig(env));
          const data = {
            firstSeen: Date.now(),
            lastSeen: Date.now(),
            requestCount: 0,
            requestsLastMinute: 0,
            lastMinuteTimestamp: Date.now(),
            notFoundCount: 0,
            incidentIds: [],
            sequentialIncidentCount: 0,
            paths: [],
            lastQueryTimestamp: 0,
            queryPageVisits: [],
            incidentsAfterQuery: 0,
            paginationUses: 0
          };
          await clientTracker.saveClientData(clientId, data);
        }

        // Return success response
        return new Response(JSON.stringify({
          success: true,
          redirectUrl: originalUrl || '/'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } else {
        // Generate a new captcha challenge for the user
        const newChallenge = await captchaSystem.generateChallenge(clientId);

        // Return error response with new captcha information
        return new Response(JSON.stringify({
          success: false,
          error: validationResult.reason,
          newCaptcha: {
            token: newChallenge.token,
            challengeId: newChallenge.challengeId
          }
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } catch (error) {
      // Generate a new captcha challenge for the user
      const newChallenge = await captchaSystem.generateChallenge(clientId);

      // Return error response with new captcha information
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid request',
        newCaptcha: {
          token: newChallenge.token,
          challengeId: newChallenge.challengeId
        }
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  // Check if this is a captcha page request
  if (url.pathname.startsWith('/captcha-challenge')) {
    // Extract parameters from the URL
    const token = url.searchParams.get('token');
    const originalUrl = url.searchParams.get('url');
    const isRefresh = url.searchParams.get('refresh') || 
                      request.headers.get('X-Refresh-Captcha') === 'true';

    // If this is a refresh request or no token is provided, generate a new challenge
    if (isRefresh || !token) {
      // Generate a new captcha challenge for the user
      const newChallenge = await captchaSystem.generateChallenge(clientId);

      // Redirect to the new captcha challenge
      const captchaUrl = new URL('https://cap.gunviolencearchive.org');
      captchaUrl.pathname = '/captcha-challenge';
      // Don't include the refresh parameter in the redirect URL to avoid redirect loops
      captchaUrl.search = `?token=${newChallenge.token}&url=${encodeURIComponent(originalUrl || request.url)}`;

      return new Response(null, {
        status: 302,
        headers: {
          'Location': captchaUrl.toString()
        }
      });
    }

    // Clean up old captchas for this client ID
    const cleanedCount = await captchaSystem.cleanupOldCaptchas(clientId, token);
    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} old captchas for client ${clientId}`);
    }

    // Get the challenge
    const challenge = await captchaSystem.getChallenge(token);

    if (!challenge) {
      // Generate a new captcha challenge for the user
      const newChallenge = await captchaSystem.generateChallenge(clientId);

      // Redirect to the new captcha challenge
      const captchaUrl = new URL('https://cap.gunviolencearchive.org');
      captchaUrl.pathname = '/captcha-challenge';
      captchaUrl.search = `?token=${newChallenge.token}&url=${encodeURIComponent(originalUrl || request.url)}`;

      return new Response(null, {
        status: 302,
        headers: {
          'Location': captchaUrl.toString()
        }
      });
    }

    // Record that we've shown a captcha to this client
    // This handles the case where a user might access the captcha page directly
    if (env && env["gva-scrapers"]) {
      try {
        const currentTime = Date.now();
        // Store the current timestamp with a 24-hour expiration
        await env["gva-scrapers"].put(`captcha-last-shown:${clientId}`, currentTime.toString(), { expirationTtl: 86400 });
      } catch (error) {
        console.error('Error setting captcha last shown timestamp:', error);
      }
    }

    // Generate the captcha HTML
    const html = captchaSystem.generateCaptchaHtml(challenge, originalUrl);

    // Return the captcha page
    return new Response(html, {
      headers: { 'Content-Type': 'text/html' }
    });
  }

  // Check if the client has already passed a captcha
  let hasPassed = false;
  // Check if the client has been flagged as suspicious
  let isFlagged = false;
  // Check if a captcha was recently shown to the client
  let captchaLastShown = 0;
  if (env && env["gva-scrapers"]) {
    try {
      hasPassed = await env["gva-scrapers"].get(`captcha-passed:${clientId}`) === 'true';
      isFlagged = await env["gva-scrapers"].get(`suspicious-flag:${clientId}`) === 'true';
      const lastShownStr = await env["gva-scrapers"].get(`captcha-last-shown:${clientId}`);
      if (lastShownStr) {
        captchaLastShown = parseInt(lastShownStr, 10);
      }
    } catch (error) {
      console.error('Error checking captcha status:', error);
    }
  }

  // If the client is flagged as suspicious and hasn't passed a captcha, show the captcha
  // We always show the captcha until they complete it
  const currentTime = Date.now();
  const twentyFourHours = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  if (isFlagged && !hasPassed) {
    // Generate a new captcha challenge
    const challenge = await captchaSystem.generateChallenge(clientId);

    // Record that we've shown a captcha to this client
    if (env && env["gva-scrapers"]) {
      try {
        // Store the current timestamp with a 24-hour expiration
        await env["gva-scrapers"].put(`captcha-last-shown:${clientId}`, currentTime.toString(), { expirationTtl: 86400 });
      } catch (error) {
        console.error('Error setting captcha last shown timestamp:', error);
      }
    }

    // Redirect to the captcha page
    const captchaUrl = new URL('https://cap.gunviolencearchive.org');
    captchaUrl.pathname = '/captcha-challenge';
    captchaUrl.search = `?token=${challenge.token}&url=${encodeURIComponent(request.url)}`;

    return new Response(null, {
      status: 302,
      headers: {
        'Location': captchaUrl.toString()
      }
    });
  }

  // Process the request
  let response;
  try {
    response = await fetch(request);
  } catch (error) {
    // If fetch fails, return a generic error response
    return new Response('An error occurred', { status: 500 });
  }

  // Track this request in our client tracker (now async)
  const clientData = await clientTracker.trackRequest(request, response.status);

  // Only perform detailed analysis for protected pages
  if (isProtectedPage) {
    // Analyze the request for scraper patterns, passing client history data
    const scraperAnalysis = analyzeForScrapers(request, clientData, config);

    // If the request is suspicious and hasn't passed a captcha, show the captcha
    // We always show the captcha until they complete it
    if (scraperAnalysis.isSuspicious && !hasPassed) {
      // Use waitUntil to not block the response
      ctx.waitUntil(logSuspiciousActivity(request, scraperAnalysis, clientData, env, config));

      // Flag this client as suspicious for all future requests
      if (env && env["gva-scrapers"]) {
        try {
          // Set a flag with the same expiration as the pattern tracking window
          const expirationTtl = Math.floor(config.patternTrackingWindow / 1000);
          await env["gva-scrapers"].put(`suspicious-flag:${clientId}`, 'true', { expirationTtl });

          // Store the current timestamp with a 24-hour expiration
          await env["gva-scrapers"].put(`captcha-last-shown:${clientId}`, currentTime.toString(), { expirationTtl: 86400 });
        } catch (error) {
          console.error('Error setting suspicious flag or captcha timestamp:', error);
        }
      }

      // Generate a new captcha challenge
      const challenge = await captchaSystem.generateChallenge(clientId);

      // Redirect to the captcha page
      const captchaUrl = new URL('https://cap.gunviolencearchive.org');
      captchaUrl.pathname = '/captcha-challenge';
      captchaUrl.search = `?token=${challenge.token}&url=${encodeURIComponent(request.url)}`;

      return new Response(null, {
        status: 302,
        headers: {
          'Location': captchaUrl.toString()
        }
      });
    }
  }

  // Return the original response
  return response;
}

/**
 * Validate mouse movement data for bot-like patterns
 * @param {Object} mouseData - Mouse movement data from the client
 * @returns {Object} - Validation result with valid flag and reason if invalid
 */
function validateMouseMovements(mouseData) {
  // If no metrics or insufficient data, we can't validate
  if (!mouseData || !mouseData.metrics || !mouseData.metrics.sufficientData) {
    return { valid: true, reason: "Insufficient data to validate" };
  }

  const metrics = mouseData.metrics;
  const botIndicators = metrics.botIndicators;
  const suspiciousFlags = [];

  // Check for bot indicators
  if (botIndicators.unnaturalStraightLines) {
    suspiciousFlags.push("Mouse movements are unnaturally straight");
  }

  if (botIndicators.teleportation) {
    suspiciousFlags.push("Mouse appears to teleport (jumps instantly)");
  }

  if (botIndicators.tooConsistentSpeed) {
    suspiciousFlags.push("Mouse speed is unnaturally consistent");
  }

  if (botIndicators.tooConsistentTiming) {
    suspiciousFlags.push("Mouse movement timing is unnaturally consistent");
  }

  if (botIndicators.tooEfficientPath) {
    suspiciousFlags.push("Mouse path is unnaturally efficient");
  }

  // Additional checks on the raw data
  if (mouseData.movements && mouseData.movements.length > 0) {
    // Check for too few movements (might be a bot that only simulates minimal movement)
    if (metrics.movementCount < 20) {
      suspiciousFlags.push("Too few mouse movements");
    }

    // Check for unusually high speeds
    if (metrics.speed.max > 5000) {
      suspiciousFlags.push("Unusually high mouse speed detected");
    }
  }

  // If we have 2 or more suspicious flags, consider it a bot
  if (suspiciousFlags.length >= 2) {
    return {
      valid: false,
      reason: "Suspicious mouse movement detected: " + suspiciousFlags.join(", ")
    };
  }

  return { valid: true };
}

/**
 * Analyze a request for signs of being a scraper
 * @param {Request} request - The request to analyze
 * @param {Object} clientData - Historical data about the client
 * @param {Object} config - Configuration object
 * @returns {Object} - Analysis results
 */
function analyzeForScrapers(request, clientData, config) {
  let suspiciousScore = 0;
  const reasons = [];

  // 1. User-Agent analysis
  const userAgent = request.headers.get('user-agent') || '';
  const lowerUserAgent = userAgent.toLowerCase();

  if (!userAgent) {
    suspiciousScore += 50;
    reasons.push('Missing User-Agent');
  } else {
    // Check for known scraper user agents
    for (const botPattern of SUSPICIOUS_USER_AGENTS) {
      if (lowerUserAgent.includes(botPattern.toLowerCase())) {
        suspiciousScore += 30;
        reasons.push(`Suspicious User-Agent: ${botPattern}`);
        break;
      }
    }

    // Check for unusually short or generic user agents
    if (userAgent.length < 30) {
      suspiciousScore += 10;
      reasons.push('Unusually short User-Agent');
    }
  }

  // 2. Header analysis
  let missingHeadersCount = 0;
  for (const header of EXPECTED_HEADERS) {
    if (!request.headers.has(header)) {
      missingHeadersCount++;
    }
  }

  if (missingHeadersCount > 0) {
    suspiciousScore += missingHeadersCount * 5; // Reduced from 10 to 5 points per missing header
    reasons.push(`Missing ${missingHeadersCount} expected headers`);
  }

  // 3. Referer analysis
  const referer = request.headers.get('referer');
  if (!referer) {
    suspiciousScore += 10; // Reduced from 20 as privacy-focused browsers might not send referers
    reasons.push('Missing Referer');
  } else if (!referer.includes('gunviolencearchive.org')) {
    suspiciousScore += 10;
    reasons.push('External Referer');
  }

  // 4. Accept header analysis
  const accept = request.headers.get('accept');
  if (!accept) {
    suspiciousScore += 10;
    reasons.push('Missing Accept header');
  } else if (accept === '*/*') {
    suspiciousScore += 5;
    reasons.push('Generic Accept header');
  }

  // 5. Cookie analysis
  const hasCookies = request.headers.has('cookie');
  if (!hasCookies) {
    suspiciousScore += 5; // Reduced from 15 as privacy-focused browsers might block cookies
    reasons.push('No cookies present');
  }

  // 6. NEW: 404 Error Pattern Analysis
  if (clientData.notFoundCount >= config.max404Threshold) {
    const score = Math.min(40, clientData.notFoundCount * 5); // Reduced from 10 to 5 points per 404, max 40 instead of 50
    suspiciousScore += score;
    reasons.push(`High number of 404 errors: ${clientData.notFoundCount}`);
  }

  // 7. NEW: Sequential Incident ID Access Analysis
  if (clientData.sequentialIncidentCount >= config.maxSequentialIncidents) {
    const score = clientData.sequentialIncidentCount * 10; // Reduced from 15 to 10 points per sequential incident
    suspiciousScore += score;
    reasons.push(`Sequential incident ID access: ${clientData.sequentialIncidentCount} in a row`);
  }

  // 8. NEW: Rate Limiting Analysis
  if (clientData.requestsLastMinute > config.maxRequestsPerMinute) {
    const score = Math.min(50, (clientData.requestsLastMinute - config.maxRequestsPerMinute) * 2);
    suspiciousScore += score;
    reasons.push(`Rate limit exceeded: ${clientData.requestsLastMinute} requests in last minute`);
  }

  // 9. NEW: Behavioral Analysis - Path Diversity
  const uniquePaths = new Set(clientData.paths.map(p => p.path)).size;
  const pathDiversity = uniquePaths / Math.max(1, clientData.paths.length);

  // Low path diversity (repeatedly hitting same URLs) is suspicious
  if (clientData.paths.length > 10 && pathDiversity < 0.3) {
    suspiciousScore += 20;
    reasons.push(`Low path diversity: ${pathDiversity.toFixed(2)}`);
  }

  // 10. NEW: Behavioral Analysis - Time Pattern
  // Check if requests are being made at suspiciously regular intervals
  if (clientData.paths.length >= 5) {
    const intervals = [];
    for (let i = 1; i < clientData.paths.length; i++) {
      intervals.push(clientData.paths[i].timestamp - clientData.paths[i-1].timestamp);
    }

    // Calculate standard deviation of intervals
    const avg = intervals.reduce((sum, val) => sum + val, 0) / intervals.length;
    const variance = intervals.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / intervals.length;
    const stdDev = Math.sqrt(variance);

    // Very consistent timing suggests automation
    if (stdDev < 500 && avg < 10000) { // Less than 500ms deviation and average under 10 seconds
      suspiciousScore += 30;
      reasons.push(`Suspiciously consistent request timing: ${stdDev.toFixed(2)}ms standard deviation`);
    }
  }

  // 11. NEW: Query Page Followed by Incident Pages Pattern
  if (clientData.incidentsAfterQuery >= 3) {
    const score = Math.min(30, clientData.incidentsAfterQuery * 5); // Reduced from 10 to 5 points per incident, max 30 instead of 50
    suspiciousScore += score;
    reasons.push(`Query-to-incident scraping pattern: ${clientData.incidentsAfterQuery} incidents accessed after query`);
  }

  // 12. NEW: Pagination Usage Pattern
  if (clientData.paginationUses >= 2) {
    const score = Math.min(30, clientData.paginationUses * 10); // Reduced from 15 to 10 points per pagination use, max 30 instead of 40
    suspiciousScore += score;
    reasons.push(`Pagination scraping pattern: Used pagination ${clientData.paginationUses} times`);
  }

  // 13. NEW: Combined Query and Pagination Pattern
  if (clientData.queryPageVisits.length >= 2 && clientData.incidentsAfterQuery >= 2 && clientData.paginationUses >= 1) {
    suspiciousScore += 25; // Reduced from 40 as legitimate users may also exhibit this pattern during research
    reasons.push('Complex scraping pattern: Query pages, pagination, and incident access');
  }

  // Determine if the request is suspicious based on the score
  const isSuspicious = suspiciousScore >= config.suspiciousThreshold;

  return {
    isSuspicious,
    suspiciousScore,
    reasons,
    userAgent,
    clientHistory: {
      requestCount: clientData.requestCount,
      notFoundCount: clientData.notFoundCount,
      sequentialIncidentCount: clientData.sequentialIncidentCount,
      requestsLastMinute: clientData.requestsLastMinute
    }
  };
}

/**
 * Log suspicious activity to Cloudflare KV
 * @param {Request} request - The suspicious request
 * @param {Object} analysis - The analysis results
 * @param {Object} clientData - Historical data about the client
 * @param {Object} env - Environment variables
 * @param {Object} config - Configuration object
 * @returns {Promise} - The logging operation
 */
async function logSuspiciousActivity(request, analysis, clientData, env, config) {
  const url = new URL(request.url);
  const clientIP = request.headers.get('cf-connecting-ip') || 'unknown';
  const timestamp = new Date().toISOString();

  // Extract relevant client history for logging
  const clientHistory = {
    firstSeen: new Date(clientData.firstSeen).toISOString(),
    totalRequests: clientData.requestCount,
    requestsLastMinute: clientData.requestsLastMinute,
    notFoundCount: clientData.notFoundCount,
    sequentialIncidentCount: clientData.sequentialIncidentCount,
    // Include the new tracking fields for query-to-incident pattern
    incidentsAfterQuery: clientData.incidentsAfterQuery,
    paginationUses: clientData.paginationUses,
    lastQueryTimestamp: clientData.lastQueryTimestamp > 0 ? new Date(clientData.lastQueryTimestamp).toISOString() : undefined,
    // Include query page visits if available
    queryPageVisits: clientData.queryPageVisits.length > 0 ? clientData.queryPageVisits.map(q => ({
      path: q.path,
      timestamp: new Date(q.timestamp).toISOString(),
      params: q.params
    })) : undefined,
    // Include the last 10 paths for context
    recentPaths: clientData.paths.slice(-10).map(p => ({
      path: p.path,
      timestamp: new Date(p.timestamp).toISOString(),
      status: p.status
    })),
    // Include incident IDs if available
    incidentIds: clientData.incidentIds.length > 0 ? clientData.incidentIds : undefined
  };

  const logData = {
    timestamp,
    clientIP,
    path: url.pathname,
    method: request.method,
    userAgent: analysis.userAgent,
    suspiciousScore: analysis.suspiciousScore,
    reasons: analysis.reasons,
    clientHistory: clientHistory,
    // Include request details
    requestDetails: {
      url: url.toString(),
      headers: Object.fromEntries([...request.headers.entries()]),
      cf: request.cf // Cloudflare-specific request data if available
    }
  };

  try {
    // Get the client tracker instance
    const clientTracker = createClientTracker(env, config);
    const clientId = clientTracker.getClientId(request);

    // Add log data to client's suspiciousLogs array
    if (!clientData.suspiciousLogs) {
      clientData.suspiciousLogs = [];
    }

    // Add the new log entry
    clientData.suspiciousLogs.push(logData);

    // Keep only the most recent 50 logs
    if (clientData.suspiciousLogs.length > 50) {
      clientData.suspiciousLogs = clientData.suspiciousLogs.slice(-50);
    }

    // Save the updated client data
    await clientTracker.saveClientData(clientId, clientData);

    // Log to console if KV is not available
    if (!env || !env["gva-scrapers"]) {
      console.error('KV not available for logging. Suspicious activity detected:', JSON.stringify({
        timestamp,
        clientIP,
        path: url.pathname,
        suspiciousScore: analysis.suspiciousScore,
        reasons: analysis.reasons
      }));
    }

    return Promise.resolve();
  } catch (error) {
    // If logging fails, log to Cloudflare console
    console.error('Scraper detection log:', JSON.stringify({
      timestamp,
      clientIP,
      path: url.pathname,
      suspiciousScore: analysis.suspiciousScore,
      reasons: analysis.reasons,
      error: error.message
    }));
    return Promise.resolve();
  }
}
